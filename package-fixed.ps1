# HustlePlug Chrome Extension Packaging Script
# Creates a zip file ready for Google Chrome Web Store upload

Write-Host "?? Packaging HustlePlug Chrome Extension..." -ForegroundColor Green

# Define the extension directory and output zip file
$extensionDir = Get-Location
$zipFileName = "hustleplug-extension-v1.0.3.zip"
$tempDir = "temp-extension-package"

# Remove existing temp directory and zip file if they exist
if (Test-Path $tempDir) {
    Remove-Item -Recurse -Force $tempDir
    Write-Host "? Cleaned up existing temp directory" -ForegroundColor Green
}

if (Test-Path $zipFileName) {
    Remove-Item -Force $zipFileName
    Write-Host "? Removed existing zip file" -ForegroundColor Green
}

# Create temporary directory for packaging
New-Item -ItemType Directory -Name $tempDir | Out-Null
Write-Host "?? Created temporary packaging directory" -ForegroundColor Green

# Define files and directories to include in the extension package
$filesToInclude = @(
    "manifest.json",
    "popup.html", 
    "popup.js",
    "background.js",
    "content.js",
    "content.css",
    "config.js"
)

$directoriesToInclude = @(
    "js",
    "styles", 
    "icons"
)

# Copy essential files
Write-Host "?? Copying essential files..." -ForegroundColor Yellow
foreach ($file in $filesToInclude) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $tempDir
        Write-Host "  ? Copied $file" -ForegroundColor Green
    } else {
        Write-Host "  ??  Warning: $file not found" -ForegroundColor Yellow
    }
}

# Copy essential directories
Write-Host "?? Copying directories..." -ForegroundColor Yellow
foreach ($dir in $directoriesToInclude) {
    if (Test-Path $dir) {
        Copy-Item -Recurse $dir -Destination $tempDir
        Write-Host "  ? Copied $dir directory" -ForegroundColor Green
    } else {
        Write-Host "  ??  Warning: $dir directory not found" -ForegroundColor Yellow
    }
}

# Remove development and test files from the temp directory
Write-Host "?? Cleaning up development files..." -ForegroundColor Yellow

$filesToRemove = @(
    "*.py",
    "test-*.js",
    "*debug*.js", 
    "*test*.js",
    "*.md",
    "*.yml",
    "*.yaml",
    ".git*",
    "node_modules",
    "package*.json",
    "eslint*",
    "*.log"
)

foreach ($pattern in $filesToRemove) {
    $itemsToRemove = Get-ChildItem -Path $tempDir -Recurse -Name $pattern -ErrorAction SilentlyContinue
    foreach ($item in $itemsToRemove) {
        $fullPath = Join-Path $tempDir $item
        if (Test-Path $fullPath) {
            Remove-Item -Recurse -Force $fullPath
            Write-Host "  ???  Removed $item" -ForegroundColor Gray
        }
    }
}

# Create the zip file
Write-Host "?? Creating zip file..." -ForegroundColor Yellow
try {
    Compress-Archive -Path "$tempDir\*" -DestinationPath $zipFileName -Force
    Write-Host "? Successfully created $zipFileName" -ForegroundColor Green
} catch {
    Write-Host "? Error creating zip file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Clean up temp directory
Remove-Item -Recurse -Force $tempDir
Write-Host "?? Cleaned up temporary files" -ForegroundColor Green

# Display package info
$zipSize = (Get-Item $zipFileName).Length / 1MB
Write-Host "`n?? Package Information:" -ForegroundColor Cyan
Write-Host "  ?? File: $zipFileName" -ForegroundColor White
Write-Host "  ?? Size: $([math]::Round($zipSize, 2)) MB" -ForegroundColor White
Write-Host "  ?? Location: $(Get-Location)\$zipFileName" -ForegroundColor White

# List contents of the zip file
Write-Host "`n?? Package Contents:" -ForegroundColor Cyan
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $zip = [System.IO.Compression.ZipFile]::OpenRead((Get-Item $zipFileName).FullName)
    $zip.Entries | ForEach-Object {
        Write-Host "  ?? $($_.FullName)" -ForegroundColor Gray
    }
    $zip.Dispose()
} catch {
    Write-Host "  ??  Could not list zip contents" -ForegroundColor Yellow
}

Write-Host "`n?? Extension packaging complete!" -ForegroundColor Green
Write-Host "`n?? Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Go to https://chrome.google.com/webstore/devconsole" -ForegroundColor White
Write-Host "  2. Sign in with your Google account" -ForegroundColor White
Write-Host "  3. Click 'Add new item' or update existing extension" -ForegroundColor White
Write-Host "  4. Upload the $zipFileName file" -ForegroundColor White
Write-Host "  5. Fill in store listing details and publish" -ForegroundColor White

Write-Host "`n??  Important Notes:" -ForegroundColor Yellow
Write-Host "  ? Make sure all permissions in manifest.json are justified" -ForegroundColor White
Write-Host "  ? Test the extension thoroughly before publishing" -ForegroundColor White
Write-Host "  ? Review Google's Chrome Web Store policies" -ForegroundColor White
Write-Host "  ? Extension version is 1.0.3 as specified in manifest.json" -ForegroundColor White 
